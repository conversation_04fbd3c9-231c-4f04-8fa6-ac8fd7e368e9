import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';
import { fileURLToPath } from 'url';

// 获取当前文件的目录路径（ES modules 中的 __dirname 替代方案）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 递归查找目录下的所有 CSV 文件
 * @param {string} directory - 要搜索的目录
 * @returns {string[]} CSV 文件路径数组
 */
function findCsvFiles(directory) {
    const csvFiles = [];
    
    function walkDir(dir) {
        const files = fs.readdirSync(dir);
        
        for (const file of files) {
            const fullPath = path.join(dir, file);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                walkDir(fullPath); // 递归遍历子目录
            } else if (file.endsWith('.csv')) {
                csvFiles.push(fullPath);
            }
        }
    }
    
    walkDir(directory);
    return csvFiles;
}

/**
 * 将字符串转换为数组
 * @param {string} value - 要转换的字符串
 * @param {boolean} isNumber - 是否转换为数字数组
 * @returns {Array} 转换后的数组
 */
function convertToList(value, isNumber = false) {
    if (!value) {
        return isNumber ? [] : [''];
    }
    
    if (isNumber) {
        return value.split(',')
            .map(x => x.trim())
            .filter(x => x)
            .map(x => parseFloat(x));
    }
    
    return value.split(',').map(x => x.trim());
}

/**
 * 根据字段前缀转换值的类型
 * @param {string} key - 字段名
 * @param {string} value - 字段值
 * @returns {*} 转换后的值
 */
function convertValue(key, value) {
    if (key.startsWith('n_')) { // 数字类型
        if (key === 'n_id') {
            return value ? parseInt(parseFloat(value)) : 0; // 取整，空值返回0
        } else {
            return value ? parseFloat(value) : 0.0; // 取浮点数，空值返回0.0
        }
    } else if (key.startsWith('s_')) { // 字符串类型
        return value ? value.trim() : ''; // 空值返回空字符串
    } else if (key.startsWith('vn_')) { // 数字数组类型
        return convertToList(value ? value.trim() : '', true);
    } else if (key.startsWith('vs_')) { // 字符串数组类型
        return convertToList(value ? value.trim() : '');
    } else if (key.startsWith('o_')) { // JSON字符串类型
        try {
            return value ? JSON.parse(value) : {}; // 解析为JSON对象，空值返回空字典
        } catch (e) {
            console.warn(`解析JSON失败: ${value}, 错误: ${e.message}`);
            return {};
        }
    } else {
        return value; // 默认返回原值
    }
}

/**
 * 处理单个 CSV 文件
 * @param {string} csvFile - CSV 文件路径
 * @returns {Promise<void>}
 */
function processCsvFile(csvFile) {
    return new Promise((resolve, reject) => {
        // 判断是否为 totals 文件（totals.csv 或 _totals.csv）
        // totals 文件只生成 _totals.json，不生成单独的 JSON 文件和 _ids.json
        const isTotals = csvFile.endsWith('_totals.csv') || csvFile.endsWith('totals.csv');
        console.log(`是否为_totals.csv: ${isTotals}`);
        
        const ids = []; // 用于存储ID和URL
        const data = []; // 用于存储其他数据
        let headers = [];
        let idColumn = null;
        
        console.log(`正在读取文件: ${csvFile}`);
        
        fs.createReadStream(csvFile)
            .pipe(csv({ separator: '\t' })) // 指定制表符为分隔符
            .on('headers', (headerList) => {
                // 修剪列名
                headers = headerList.map(name => name.trim());
                console.log(`列名: ${headers}`);
                
                // 确定ID列名
                if (headers.includes('n_id')) {
                    idColumn = 'n_id';
                } else if (headers.includes('tag')) {
                    idColumn = 'tag';
                } else {
                    console.log(`警告: 文件 ${csvFile} 没有找到ID列 (n_id 或 tag)，跳过此文件`);
                    return reject(new Error('未找到ID列'));
                }
            })
            .on('data', (row) => {
                // 根据前缀转换字段类型
                for (const key in row) {
                    row[key] = convertValue(key, row[key]);
                }
                
                // 获取ID值
                let idValue = row[idColumn];
                
                // 确保ID值是整数
                if (typeof idValue === 'string' && /^\d+$/.test(idValue)) {
                    idValue = parseInt(idValue);
                }
                
                // 只有非totals文件才需要生成ID和URL
                if (!isTotals) {
                    // 检查ID是否已存在
                    if (!ids.some(entry => entry.id === idValue)) {
                        // 生成JSON文件路径
                        const jsonFilePath = path.join(path.dirname(csvFile), `${idValue}.json`);
                        const relativePath = './' + path.relative(path.dirname(__filename), jsonFilePath).replace(/\\/g, '/');

                        ids.push({
                            id: idValue,
                            url: relativePath
                        });
                    }
                }
                
                // 创建一个新的对象，去掉前缀，并过滤掉key为'备注'的字段
                const newRow = {};
                for (const [key, value] of Object.entries(row)) {
                    // 获取不带前缀的键名
                    const keyWithoutPrefix = key.includes('_') ? key.split('_').slice(1).join('_') : key;
                    
                    // 如果键名不是'备注'，则添加到新对象中
                    if (keyWithoutPrefix !== '备注') {
                        newRow[keyWithoutPrefix] = value;
                    }
                }
                
                data.push(newRow);
                
                if (!isTotals) {
                    // 将每一行数据写入对应的JSON文件
                    const jsonFilePath = path.join(path.dirname(csvFile), `${idValue}.json`);
                    fs.writeFileSync(jsonFilePath, JSON.stringify(newRow, null, 4), 'utf8');
                }
            })
            .on('end', () => {
                try {
                    if (!isTotals) {
                        // 将所有ID和URL写入_ids.json文件
                        const idsFilePath = path.join(path.dirname(csvFile), '_ids.json');
                        console.log(`生成的ID和URL: ${JSON.stringify(ids, null, 2)}`);
                        if (ids.length > 0) {
                            fs.writeFileSync(idsFilePath, JSON.stringify(ids, null, 4), 'utf8');
                        }
                    }
                    
                    if (isTotals) {
                        // 将所有数据写入_totals.json文件
                        const totalsFilePath = path.join(path.dirname(csvFile), '_totals.json');
                        if (data.length > 0) {
                            fs.writeFileSync(totalsFilePath, JSON.stringify(data, null, 4), 'utf8');
                        }
                    }
                    
                    resolve();
                } catch (error) {
                    reject(error);
                }
            })
            .on('error', (error) => {
                reject(error);
            });
    });
}

/**
 * 主函数：CSV 转 JSON
 */
async function csvToJson() {
    const currentDirectory = process.cwd(); // 获取当前工作目录
    console.log(`当前工作目录: ${currentDirectory}`);
    
    const csvFiles = findCsvFiles(currentDirectory); // 查找所有CSV文件
    
    if (csvFiles.length === 0) {
        console.log("没有找到CSV文件，请确保当前目录及其子目录下有CSV文件。");
        return;
    }
    
    console.log(`找到的CSV文件: ${csvFiles}`);
    
    for (const csvFile of csvFiles) {
        try {
            await processCsvFile(csvFile);
            console.log(`成功处理文件: ${csvFile}`);
        } catch (error) {
            console.error(`处理文件 ${csvFile} 时出错: ${error.message}`);
        }
    }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
    csvToJson().catch(console.error);
}

export {
    csvToJson,
    findCsvFiles,
    convertValue,
    convertToList
};
