#!/usr/bin/env node
/**
 * 腾讯云 COS JSON 文件一键上传脚本
 * 直接使用腾讯云 COS SDK 实现
 * 支持增量上传（基于 MD5 检查）
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';
import { glob } from 'glob';
import COS from 'cos-nodejs-sdk-v5';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 导入配置
import { COS_CONFIG } from './tencentEnv.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从环境变量获取配置版本，默认为 'pre-version'
const CONFIG_VERSION = process.env.CONFIG_VERSION || 'pre-version';

// 配置
const CONFIG = {
  // 本地 JSON 文件目录
  localDirectory: __dirname,
  // COS 目标目录前缀（使用环境变量配置）
  cosPrefix: `website_public/_config/${CONFIG_VERSION}/`,
  // MD5 记录文件（使用下载目录中的 MD5 文件）
  md5File: path.join(__dirname, 'downloaded_config', 'md5.json'),
  // 排除的目录
  // 排除的目录
  excludeDirs: [
    'node_modules', '.git', '.svn', '.hg',
    'dist', 'build', 'out', '.next',
    '.vscode', '.idea',
    '__pycache__', '.pytest_cache',
    'coverage', '.nyc_output',
    'logs', 'tmp', 'temp',
    'cos_upload_env',
    'downloaded_config'  // 排除下载目录
  ],
  // 排除的文件
  excludeFiles: ['md5.json', 'upload_md5.json', 'package.json', 'package-lock.json', '.env']
};

/**
 * MD5 管理器
 */
class MD5Manager {
  constructor(md5FilePath = CONFIG.md5File) {
    this.md5FilePath = md5FilePath;
    this.md5Records = {};
    this.loadMD5Records();
  }

  /**
   * 加载 MD5 记录
   */
  loadMD5Records() {
    try {
      if (fs.existsSync(this.md5FilePath)) {
        const data = fs.readFileSync(this.md5FilePath, 'utf8');
        const parsedData = JSON.parse(data);

        // 检查是否是下载工具生成的格式（包含 files 字段）
        if (parsedData.files && typeof parsedData.files === 'object') {
          // 转换下载格式到上传格式
          this.md5Records = {};
          Object.keys(parsedData.files).forEach(filePath => {
            const fileInfo = parsedData.files[filePath];
            this.md5Records[filePath] = fileInfo.md5;
          });
          console.log(`📋 已从下载 MD5 文件加载 ${Object.keys(this.md5Records).length} 个文件的 MD5 记录`);
        } else {
          // 原有的上传格式
          this.md5Records = parsedData;
          console.log(`📋 已加载 ${Object.keys(this.md5Records).length} 个文件的 MD5 记录`);
        }
      } else {
        console.log('📋 MD5 记录文件不存在，将创建新的记录');
        this.md5Records = {};
      }
    } catch (error) {
      console.error('加载 MD5 记录失败:', error.message);
      this.md5Records = {};
    }
  }

  /**
   * 保存 MD5 记录
   */
  saveMD5Records() {
    try {
      // 保存为上传工具专用的 MD5 文件，避免覆盖下载的 MD5 文件
      const uploadMd5FilePath = path.join(path.dirname(this.md5FilePath), 'upload_md5.json');
      fs.writeFileSync(uploadMd5FilePath, JSON.stringify(this.md5Records, null, 2), 'utf8');
      console.log(`💾 已保存 ${Object.keys(this.md5Records).length} 个文件的 MD5 记录到 ${uploadMd5FilePath}`);
    } catch (error) {
      console.error('保存 MD5 记录失败:', error.message);
    }
  }

  /**
   * 计算文件 MD5
   */
  calculateFileMD5(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      // 标准化行结束符
      const normalizedContent = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
      return crypto.createHash('md5').update(normalizedContent, 'utf8').digest('hex');
    } catch (error) {
      console.error(`计算文件 ${filePath} MD5 失败:`, error.message);
      return '';
    }
  }

  /**
   * 检查文件是否发生变化
   */
  hasFileChanged(filePath) {
    const currentMD5 = this.calculateFileMD5(filePath);
    if (!currentMD5) return false;

    // 使用相对路径作为键
    const relativePath = path.relative(CONFIG.localDirectory, filePath).replace(/\\/g, '/');
    const storedMD5 = this.md5Records[relativePath];

    if (!storedMD5) {
      console.log(`🆕 新文件: ${relativePath}`);
      return true;
    }

    if (storedMD5 !== currentMD5) {
      console.log(`🔄 文件已变化: ${relativePath}`);
      return true;
    }

    return false;
  }

  /**
   * 更新文件 MD5 记录
   */
  updateFileMD5(filePath) {
    const currentMD5 = this.calculateFileMD5(filePath);
    if (currentMD5) {
      // 使用相对路径作为键
      const relativePath = path.relative(CONFIG.localDirectory, filePath).replace(/\\/g, '/');
      this.md5Records[relativePath] = currentMD5;
    }
  }

  /**
   * 获取发生变化的文件列表
   */
  getChangedFiles(fileList) {
    return fileList.filter(filePath => this.hasFileChanged(filePath));
  }
}

/**
 * JSON 文件上传器
 */
class JSONFileUploader {
  constructor() {
    this.md5Manager = new MD5Manager();
    this.uploadResults = [];
    this.cosClient = null;
    this.initializeCOSClient();

    // 显示当前配置
    console.log(`📁 配置版本: ${CONFIG_VERSION}`);
    console.log(`🎯 上传目标: ${CONFIG.cosPrefix}`);
  }

  /**
   * 初始化 COS 客户端
   */
  initializeCOSClient() {
    try {
      this.cosClient = new COS({
        SecretId: COS_CONFIG.secretId,
        SecretKey: COS_CONFIG.secretKey,
        Protocol: COS_CONFIG.protocol,
        Domain: COS_CONFIG.domain || `${COS_CONFIG.bucket}.cos.${COS_CONFIG.region}.myqcloud.com`
      });
      console.log('✅ COS 客户端初始化成功');
    } catch (error) {
      console.error('❌ COS 客户端初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 查找所有 JSON 文件
   */
  async findJSONFiles(directory = CONFIG.localDirectory) {
    try {
      console.log(`🔍 扫描目录: ${path.resolve(directory)}`);
      
      // 构建 glob 模式，排除指定目录
      const excludePattern = `{${CONFIG.excludeDirs.join(',')}}`;
      const pattern = '**/*.json';
      
      const files = await glob(pattern, {
        cwd: directory,
        ignore: [
          `${excludePattern}/**`,
          ...CONFIG.excludeFiles
        ]
      });

      // 转换为相对路径
      const jsonFiles = files.map(file => path.normalize(file).replace(/\\/g, '/'));
      
      console.log(`✅ 找到 ${jsonFiles.length} 个 JSON 文件`);
      
      if (jsonFiles.length > 0) {
        console.log('📋 文件列表:');
        jsonFiles.forEach((file, index) => {
          const filePath = path.join(directory, file);
          const stats = fs.statSync(filePath);
          const sizeKB = (stats.size / 1024).toFixed(1);
          console.log(`  ${(index + 1).toString().padStart(2)}. ${file} (${sizeKB} KB)`);
        });
      }

      return jsonFiles;
    } catch (error) {
      console.error('查找 JSON 文件失败:', error.message);
      return [];
    }
  }

  /**
   * 上传单个文件
   */
  async uploadSingleFile(filePath, objectKey) {
    try {
      console.log(`📤 上传文件: ${filePath} -> ${objectKey}`);

      // 读取文件
      const fileContent = fs.readFileSync(filePath);
      const fileSize = fileContent.length;

      // 使用 COS SDK 上传文件
      const result = await new Promise((resolve, reject) => {
        this.cosClient.putObject({
          Bucket: COS_CONFIG.bucket,
          Region: COS_CONFIG.region,
          Key: objectKey,
          Body: fileContent,
          ContentType: 'application/json'
        }, (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });

      console.log(`✅ 上传成功: ${filePath}`);
      return {
        filePath,
        objectKey,
        status: 'success',
        size: fileSize,
        location: result.Location,
        etag: result.ETag
      };

    } catch (error) {
      console.error(`❌ 上传失败: ${filePath} - ${error.message}`);
      return {
        filePath,
        objectKey,
        status: 'failure',
        error: error.message
      };
    }
  }

  /**
   * 批量上传 JSON 文件
   */
  async uploadAllJSONFiles(directory = CONFIG.localDirectory) {
    try {
      console.log('🚀 开始 JSON 文件上传任务...');
      
      // 查找所有 JSON 文件
      const jsonFiles = await this.findJSONFiles(directory);
      
      if (jsonFiles.length === 0) {
        console.log('⚠️ 没有找到需要上传的 JSON 文件');
        return [];
      }

      // 检查哪些文件发生了变化
      console.log(`🔍 检查 ${jsonFiles.length} 个文件的 MD5 变化...`);
      const changedFiles = this.md5Manager.getChangedFiles(jsonFiles);

      if (changedFiles.length === 0) {
        console.log('🎉 所有 JSON 文件都没有变化，无需上传');
        return [];
      }

      console.log(`🔄 发现 ${changedFiles.length} 个文件发生变化，开始上传...`);

      // 上传变化的文件
      const results = [];
      let successCount = 0;
      let failureCount = 0;
      const successfulFiles = [];

      for (let i = 0; i < changedFiles.length; i++) {
        const filePath = changedFiles[i];
        const objectKey = CONFIG.cosPrefix + filePath;
        
        console.log(`[${i + 1}/${changedFiles.length}] 处理: ${filePath}`);
        
        const result = await this.uploadSingleFile(
          path.join(directory, filePath), 
          objectKey
        );
        
        results.push(result);

        if (result.status === 'success') {
          successCount++;
          successfulFiles.push(filePath);
        } else {
          failureCount++;
        }

        // 添加延迟避免请求过于密集
        if (i < changedFiles.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // 更新成功上传文件的 MD5 记录
      if (successfulFiles.length > 0) {
        console.log(`📝 更新 ${successfulFiles.length} 个成功上传文件的 MD5 记录...`);
        successfulFiles.forEach(filePath => {
          this.md5Manager.updateFileMD5(path.join(directory, filePath));
        });
        this.md5Manager.saveMD5Records();
      }

      console.log(`🎯 上传完成! 成功: ${successCount}, 失败: ${failureCount}`);
      this.uploadResults = results;
      return results;

    } catch (error) {
      console.error('批量上传过程中发生错误:', error.message);
      throw error;
    }
  }

  /**
   * 生成上传报告
   */
  generateUploadReport() {
    if (this.uploadResults.length === 0) {
      return '没有上传结果可报告';
    }

    const successFiles = this.uploadResults.filter(r => r.status === 'success');
    const failureFiles = this.uploadResults.filter(r => r.status === 'failure');
    
    const report = [
      '='.repeat(60),
      'JSON 文件上传报告',
      '='.repeat(60),
      `上传时间: ${new Date().toLocaleString()}`,
      `目标前缀: ${CONFIG.cosPrefix}`,
      '',
      `总文件数: ${this.uploadResults.length}`,
      `成功上传: ${successFiles.length}`,
      `上传失败: ${failureFiles.length}`,
      ''
    ];

    if (successFiles.length > 0) {
      report.push('成功上传的文件:', '-'.repeat(40));
      successFiles.forEach(result => {
        const sizeMB = (result.size / 1024 / 1024).toFixed(2);
        report.push(`✅ ${result.filePath} (${sizeMB} MB)`);
      });
      report.push('');
    }

    if (failureFiles.length > 0) {
      report.push('上传失败的文件:', '-'.repeat(40));
      failureFiles.forEach(result => {
        report.push(`❌ ${result.filePath}`);
        report.push(`   错误: ${result.error}`);
      });
      report.push('');
    }

    report.push('='.repeat(60));
    return report.join('\n');
  }

  /**
   * 保存上传报告
   */
  saveUploadReport(filename = 'upload_report.txt') {
    try {
      const report = this.generateUploadReport();
      fs.writeFileSync(filename, report, 'utf8');
      console.log(`📄 上传报告已保存到: ${filename}`);
    } catch (error) {
      console.error('保存上传报告失败:', error.message);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🔗 JSON 文件一键上传工具');
    console.log('📡 基于 cosService.js 腾讯云 COS 接口');
    console.log('🔄 支持 MD5 增量上传');
    console.log('=' * 60);

    const uploader = new JSONFileUploader();
    
    // 检查命令行参数
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
      console.log('使用方法:');
      console.log('  node upload-json-files.js          # 正常上传模式');
      console.log('  node upload-json-files.js --init   # 初始化 MD5 记录');
      console.log('  node upload-json-files.js --help   # 显示帮助');
      return;
    }

    if (args.includes('--init')) {
      console.log('🔄 初始化 MD5 记录模式...');
      const jsonFiles = await uploader.findJSONFiles();
      jsonFiles.forEach(filePath => {
        uploader.md5Manager.updateFileMD5(path.join(CONFIG.localDirectory, filePath));
      });
      uploader.md5Manager.saveMD5Records();
      console.log('✅ MD5 记录初始化完成!');
      return;
    }

    // 执行上传
    const results = await uploader.uploadAllJSONFiles();
    
    // 生成并显示报告
    const report = uploader.generateUploadReport();
    console.log('\n' + report);
    
    // 保存报告
    uploader.saveUploadReport();
    
    // 检查是否有失败的上传
    const failureCount = results.filter(r => r.status === 'failure').length;
    if (failureCount > 0) {
      console.log(`⚠️ 有 ${failureCount} 个文件上传失败`);
      process.exit(1);
    } else {
      console.log('🎉 所有文件上传成功!');
    }

  } catch (error) {
    console.error('程序执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { JSONFileUploader, MD5Manager };
