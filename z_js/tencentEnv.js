/**
 * 腾讯云COS配置项
 * @type {{
 *   secretId: string,     // 腾讯云SecretId
 *   secretKey: string,    // 腾讯云SecretKey
 *   region: string,       // COS区域
 *   bucket: string,       // Bucket名称
 *   domain: string,       // 自定义域名（可选）
 *   protocol: string,     // 协议，https或http
 *   serviceType: string   // 服务类型，固定为'cos'
 * }}
 */
export const COS_CONFIG = {
  // 腾讯云访问密钥，请替换为实际值
  // 建议通过环境变量或其他安全方式管理密钥
  secretId: 'IKIDxmNn24uhOtFUYR9e5NSRJzXs3kdO0WCm',
  secretKey: 'tfGn6B6thNyNH1hKqV3PZZRDxd9SddQs',
  
  // COS服务区域，常用区域：
  // ap-beijing: 北京
  // ap-shanghai: 上海  
  // ap-guangzhou: 广州
  // ap-chengdu: 成都
  // ap-singapore: 新加坡
  // ap-hongkong: 香港
  region: 'ap-singapore',
  
  // Bucket名称，格式：BucketName-APPID
  bucket: 'satworld-res-1323539502',
  
  // 自定义域名（可选），如果使用CDN加速域名可在此配置
  // 在开发环境中使用本地代理来避免CORS问题
  domain: '',
  
  // 协议类型
  protocol: 'https:',
  
  // 服务类型标识
  serviceType: 'cos'
};

/**
 * 获取COS完整的服务域名
 * @returns {string} COS服务域名
 */
export function getCOSServiceDomain() {
  if (COS_CONFIG.domain) {
    return COS_CONFIG.domain;
  }
  return `${COS_CONFIG.bucket}.cos.${COS_CONFIG.region}.myqcloud.com`;
}

/**
 * 获取COS完整的服务URL
 * @returns {string} COS服务完整URL
 */
export function getCOSServiceUrl() {
  return `${COS_CONFIG.protocol}//${getCOSServiceDomain()}`;
}

/**
 * 验证COS配置是否完整
 * @returns {boolean} 配置是否有效
 */
export function validateCOSConfig() {
  const { secretId, secretKey, region, bucket } = COS_CONFIG;
  
  if (!secretId || secretId.includes('REPLACE_WITH')) {
    console.error('腾讯云COS SecretId 未正确配置');
    return false;
  }
  
  if (!secretKey || secretKey.includes('REPLACE_WITH')) {
    console.error('腾讯云COS SecretKey 未正确配置');
    return false;
  }
  
  if (!region) {
    console.error('腾讯云COS 区域未配置');
    return false;
  }
  
  if (!bucket) {
    console.error('腾讯云COS Bucket名称未配置');
    return false;
  }
  
  return true;
}
