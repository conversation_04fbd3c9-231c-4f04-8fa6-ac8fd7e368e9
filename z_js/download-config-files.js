#!/usr/bin/env node
/**
 * 腾讯云 COS 配置文件下载脚本
 * 从指定的 COS 目录下载所有文件并生成 MD5 校验文件
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';
import COS from 'cos-nodejs-sdk-v5';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 导入配置
import { COS_CONFIG } from './tencentEnv.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从环境变量获取配置版本，默认为 'pre-version'
const CONFIG_VERSION = process.env.CONFIG_VERSION || 'pre-version';

// 配置
const CONFIG = {
  // COS 源目录前缀（使用环境变量配置）
  cosPrefix: `website_public/_config/${CONFIG_VERSION}/`,
  // 本地下载目录
  downloadDirectory: path.join(__dirname, 'downloaded_config'),
  // MD5 校验文件
  md5File: 'md5.json',
  // 下载报告文件
  reportFile: 'download_report.txt'
};

/**
 * 配置文件下载器
 */
class ConfigFileDownloader {
  constructor() {
    this.cosClient = null;
    this.downloadResults = [];
    this.md5Records = {};
    this.initializeCOSClient();
    this.ensureDownloadDirectory();

    // 显示当前配置
    console.log(`📁 配置版本: ${CONFIG_VERSION}`);
    console.log(`🎯 下载源: ${CONFIG.cosPrefix}`);
    console.log(`📂 下载目录: ${CONFIG.downloadDirectory}`);
  }

  /**
   * 初始化 COS 客户端
   */
  initializeCOSClient() {
    try {
      this.cosClient = new COS({
        SecretId: COS_CONFIG.secretId,
        SecretKey: COS_CONFIG.secretKey,
        Protocol: COS_CONFIG.protocol,
        Domain: COS_CONFIG.domain || `${COS_CONFIG.bucket}.cos.${COS_CONFIG.region}.myqcloud.com`
      });
      console.log('✅ COS 客户端初始化成功');
    } catch (error) {
      console.error('❌ COS 客户端初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 确保下载目录存在
   */
  ensureDownloadDirectory() {
    try {
      if (!fs.existsSync(CONFIG.downloadDirectory)) {
        fs.mkdirSync(CONFIG.downloadDirectory, { recursive: true });
        console.log(`📁 创建下载目录: ${CONFIG.downloadDirectory}`);
      }
    } catch (error) {
      console.error('创建下载目录失败:', error.message);
      throw error;
    }
  }

  /**
   * 列出 COS 指定前缀下的所有文件
   */
  async listCOSFiles() {
    try {
      console.log(`🔍 扫描 COS 目录: ${CONFIG.cosPrefix}`);
      
      const allFiles = [];
      let marker = '';
      let isTruncated = true;

      while (isTruncated) {
        const result = await new Promise((resolve, reject) => {
          this.cosClient.getBucket({
            Bucket: COS_CONFIG.bucket,
            Region: COS_CONFIG.region,
            Prefix: CONFIG.cosPrefix,
            Marker: marker,
            MaxKeys: 1000
          }, (err, data) => {
            if (err) {
              reject(err);
            } else {
              resolve(data);
            }
          });
        });

        if (result.Contents) {
          // 过滤掉目录条目（以 / 结尾的条目）
          const files = result.Contents.filter(file => !file.Key.endsWith('/'));
          allFiles.push(...files);
        }

        isTruncated = result.IsTruncated === 'true';
        if (isTruncated && result.NextMarker) {
          marker = result.NextMarker;
        } else {
          break;
        }
      }

      console.log(`✅ 找到 ${allFiles.length} 个文件`);
      
      if (allFiles.length > 0) {
        console.log('📋 文件列表:');
        allFiles.forEach((file, index) => {
          const sizeKB = (parseInt(file.Size) / 1024).toFixed(1);
          const relativePath = file.Key.replace(CONFIG.cosPrefix, '');
          console.log(`  ${(index + 1).toString().padStart(2)}. ${relativePath} (${sizeKB} KB)`);
        });
      }

      return allFiles;
    } catch (error) {
      console.error('列出 COS 文件失败:', error.message);
      throw error;
    }
  }

  /**
   * 下载单个文件
   */
  async downloadSingleFile(cosFile) {
    try {
      const relativePath = cosFile.Key.replace(CONFIG.cosPrefix, '');
      const localFilePath = path.join(CONFIG.downloadDirectory, relativePath);
      
      // 确保本地目录存在
      const localDir = path.dirname(localFilePath);
      if (!fs.existsSync(localDir)) {
        fs.mkdirSync(localDir, { recursive: true });
      }

      console.log(`📥 下载文件: ${relativePath}`);

      // 使用 COS SDK 下载文件
      const result = await new Promise((resolve, reject) => {
        this.cosClient.getObject({
          Bucket: COS_CONFIG.bucket,
          Region: COS_CONFIG.region,
          Key: cosFile.Key
        }, (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });

      // 保存文件到本地
      fs.writeFileSync(localFilePath, result.Body);

      // 计算文件 MD5
      const md5Hash = this.calculateFileMD5(localFilePath);

      console.log(`✅ 下载成功: ${relativePath}`);
      
      return {
        cosKey: cosFile.Key,
        relativePath,
        localPath: localFilePath,
        status: 'success',
        size: parseInt(cosFile.Size),
        md5: md5Hash,
        lastModified: cosFile.LastModified
      };

    } catch (error) {
      const relativePath = cosFile.Key.replace(CONFIG.cosPrefix, '');
      console.error(`❌ 下载失败: ${relativePath} - ${error.message}`);
      
      return {
        cosKey: cosFile.Key,
        relativePath,
        status: 'failure',
        error: error.message
      };
    }
  }

  /**
   * 计算文件 MD5
   */
  calculateFileMD5(filePath) {
    try {
      const content = fs.readFileSync(filePath);
      return crypto.createHash('md5').update(content).digest('hex');
    } catch (error) {
      console.error(`计算文件 ${filePath} MD5 失败:`, error.message);
      return '';
    }
  }

  /**
   * 批量下载所有文件
   */
  async downloadAllFiles() {
    try {
      console.log('🚀 开始配置文件下载任务...');
      
      // 列出所有文件
      const cosFiles = await this.listCOSFiles();
      
      if (cosFiles.length === 0) {
        console.log('⚠️ 没有找到需要下载的文件');
        return [];
      }

      console.log(`📥 开始下载 ${cosFiles.length} 个文件...`);

      // 下载所有文件
      const results = [];
      let successCount = 0;
      let failureCount = 0;

      for (let i = 0; i < cosFiles.length; i++) {
        const cosFile = cosFiles[i];
        
        console.log(`[${i + 1}/${cosFiles.length}] 处理: ${cosFile.Key.replace(CONFIG.cosPrefix, '')}`);
        
        const result = await this.downloadSingleFile(cosFile);
        results.push(result);

        if (result.status === 'success') {
          successCount++;
          // 记录 MD5
          this.md5Records[result.relativePath] = {
            md5: result.md5,
            size: result.size,
            lastModified: result.lastModified,
            downloadTime: new Date().toISOString()
          };
        } else {
          failureCount++;
        }

        // 添加延迟避免请求过于密集
        if (i < cosFiles.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log(`🎯 下载完成! 成功: ${successCount}, 失败: ${failureCount}`);
      this.downloadResults = results;
      
      // 生成 MD5 校验文件
      await this.generateMD5File();
      
      return results;

    } catch (error) {
      console.error('批量下载过程中发生错误:', error.message);
      throw error;
    }
  }

  /**
   * 生成 MD5 校验文件
   */
  async generateMD5File() {
    try {
      const md5FilePath = path.join(CONFIG.downloadDirectory, CONFIG.md5File);
      
      const md5Data = {
        generatedAt: new Date().toISOString(),
        configVersion: CONFIG_VERSION,
        cosPrefix: CONFIG.cosPrefix,
        totalFiles: Object.keys(this.md5Records).length,
        files: this.md5Records
      };

      fs.writeFileSync(md5FilePath, JSON.stringify(md5Data, null, 2), 'utf8');
      console.log(`💾 MD5 校验文件已生成: ${md5FilePath}`);
      console.log(`📊 包含 ${Object.keys(this.md5Records).length} 个文件的校验信息`);
      
    } catch (error) {
      console.error('生成 MD5 校验文件失败:', error.message);
    }
  }

  /**
   * 生成下载报告
   */
  generateDownloadReport() {
    if (this.downloadResults.length === 0) {
      return '没有下载结果可报告';
    }

    const successFiles = this.downloadResults.filter(r => r.status === 'success');
    const failureFiles = this.downloadResults.filter(r => r.status === 'failure');
    
    const totalSize = successFiles.reduce((sum, file) => sum + file.size, 0);
    const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);
    
    const report = [
      '='.repeat(60),
      '配置文件下载报告',
      '='.repeat(60),
      `下载时间: ${new Date().toLocaleString()}`,
      `配置版本: ${CONFIG_VERSION}`,
      `源目录: ${CONFIG.cosPrefix}`,
      `下载目录: ${CONFIG.downloadDirectory}`,
      '',
      `总文件数: ${this.downloadResults.length}`,
      `成功下载: ${successFiles.length}`,
      `下载失败: ${failureFiles.length}`,
      `总大小: ${totalSizeMB} MB`,
      ''
    ];

    if (successFiles.length > 0) {
      report.push('成功下载的文件:', '-'.repeat(40));
      successFiles.forEach(result => {
        const sizeMB = (result.size / 1024 / 1024).toFixed(2);
        report.push(`✅ ${result.relativePath} (${sizeMB} MB)`);
        report.push(`   MD5: ${result.md5}`);
      });
      report.push('');
    }

    if (failureFiles.length > 0) {
      report.push('下载失败的文件:', '-'.repeat(40));
      failureFiles.forEach(result => {
        report.push(`❌ ${result.relativePath}`);
        report.push(`   错误: ${result.error}`);
      });
      report.push('');
    }

    report.push('='.repeat(60));
    return report.join('\n');
  }

  /**
   * 保存下载报告
   */
  saveDownloadReport() {
    try {
      const reportPath = path.join(CONFIG.downloadDirectory, CONFIG.reportFile);
      const report = this.generateDownloadReport();
      fs.writeFileSync(reportPath, report, 'utf8');
      console.log(`📄 下载报告已保存到: ${reportPath}`);
    } catch (error) {
      console.error('保存下载报告失败:', error.message);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🔗 配置文件下载工具');
    console.log('📡 基于腾讯云 COS SDK');
    console.log('🔐 自动生成 MD5 校验文件');
    console.log('='.repeat(60));

    const downloader = new ConfigFileDownloader();
    
    // 检查命令行参数
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
      console.log('使用方法:');
      console.log('  node download-config-files.js          # 下载配置文件');
      console.log('  node download-config-files.js --help   # 显示帮助');
      console.log('');
      console.log('环境变量:');
      console.log('  CONFIG_VERSION                         # 配置版本 (默认: pre-version)');
      return;
    }

    // 执行下载
    const results = await downloader.downloadAllFiles();
    
    // 生成并显示报告
    const report = downloader.generateDownloadReport();
    console.log('\n' + report);
    
    // 保存报告
    downloader.saveDownloadReport();
    
    // 检查是否有失败的下载
    const failureCount = results.filter(r => r.status === 'failure').length;
    if (failureCount > 0) {
      console.log(`⚠️ 有 ${failureCount} 个文件下载失败`);
      process.exit(1);
    } else {
      console.log('🎉 所有文件下载成功!');
    }

  } catch (error) {
    console.error('程序执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { ConfigFileDownloader };
