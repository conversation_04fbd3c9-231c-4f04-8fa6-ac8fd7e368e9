#!/usr/bin/env node
/**
 * 配置文件同步工作流脚本
 * 
 * 功能：
 * 1. 检查是否存在下载缓存目录
 * 2. 如果不存在缓存，优先执行下载功能
 * 3. 下载完成后，根据下载的 md5.json，使用上传脚本上传差异文件
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { ConfigFileDownloader } from './download-config-files.js';
import { JSONFileUploader } from './upload-json-files.js';

// 加载环境变量
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 验证并获取配置版本
 */
function validateAndGetConfigVersion() {
  const configVersion = process.env.CONFIG_VERSION;

  if (!configVersion) {
    console.error('❌ 错误: 未设置配置版本');
    console.error('请在 .env 文件中设置 CONFIG_VERSION 或通过环境变量指定');
    console.error('例如: CONFIG_VERSION=pre-version');
    console.error('可选值: dev, test, pre-version, staging, production, release');
    process.exit(1);
  }

  return configVersion;
}

// 配置
const CONFIG = {
  downloadCacheDir: path.join(__dirname, 'downloaded_config'),
  md5File: path.join(__dirname, 'downloaded_config', 'md5.json'),
  reportFile: path.join(__dirname, 'workflow_report.txt')
};

/**
 * 工作流管理器
 */
class WorkflowManager {
  constructor() {
    // 验证配置版本
    this.configVersion = validateAndGetConfigVersion();

    this.results = {
      downloadExecuted: false,
      downloadSuccess: false,
      uploadExecuted: false,
      uploadSuccess: false,
      downloadResults: [],
      uploadResults: [],
      errors: []
    };
  }

  /**
   * 检查下载缓存是否存在且配置版本匹配
   */
  checkDownloadCache() {
    const cacheExists = fs.existsSync(CONFIG.downloadCacheDir);
    const md5Exists = fs.existsSync(CONFIG.md5File);

    console.log(`📁 检查下载缓存目录: ${CONFIG.downloadCacheDir}`);
    console.log(`   目录存在: ${cacheExists ? '✅' : '❌'}`);
    console.log(`   MD5文件存在: ${md5Exists ? '✅' : '❌'}`);

    if (!cacheExists || !md5Exists) {
      return false;
    }

    // 检查配置版本是否匹配
    try {
      const md5Data = JSON.parse(fs.readFileSync(CONFIG.md5File, 'utf8'));
      const cachedVersion = md5Data.configVersion;
      const currentVersion = this.configVersion;

      console.log(`   缓存配置版本: ${cachedVersion}`);
      console.log(`   当前配置版本: ${currentVersion}`);

      if (cachedVersion !== currentVersion) {
        console.log(`⚠️ 配置版本不匹配，需要重新下载`);
        console.log(`   缓存版本: ${cachedVersion}`);
        console.log(`   目标版本: ${currentVersion}`);
        return false;
      }

      console.log(`✅ 配置版本匹配: ${currentVersion}`);
      return true;

    } catch (error) {
      console.log(`❌ 读取MD5文件失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 执行下载流程
   */
  async executeDownload() {
    try {
      console.log('\n🚀 开始执行下载流程...');
      console.log('='.repeat(60));
      
      this.results.downloadExecuted = true;
      
      const downloader = new ConfigFileDownloader();
      const downloadResults = await downloader.downloadAllFiles();
      
      this.results.downloadResults = downloadResults;
      
      // 检查下载是否成功
      const failureCount = downloadResults.filter(r => r.status === 'failure').length;
      this.results.downloadSuccess = failureCount === 0;
      
      if (this.results.downloadSuccess) {
        console.log('✅ 下载流程执行成功');
      } else {
        console.log(`⚠️ 下载流程部分失败，${failureCount} 个文件下载失败`);
        this.results.errors.push(`下载失败文件数: ${failureCount}`);
      }
      
      return this.results.downloadSuccess;
      
    } catch (error) {
      console.error('❌ 下载流程执行失败:', error.message);
      this.results.downloadSuccess = false;
      this.results.errors.push(`下载流程错误: ${error.message}`);
      return false;
    }
  }

  /**
   * 执行上传流程
   */
  async executeUpload() {
    try {
      console.log('\n🚀 开始执行上传流程...');
      console.log('='.repeat(60));
      
      this.results.uploadExecuted = true;
      
      const uploader = new JSONFileUploader();
      const uploadResults = await uploader.uploadAllJSONFiles();
      
      this.results.uploadResults = uploadResults;
      
      // 检查上传是否成功
      const failureCount = uploadResults.filter(r => r.status === 'failure').length;
      this.results.uploadSuccess = failureCount === 0;
      
      if (uploadResults.length === 0) {
        console.log('✅ 上传流程完成 - 没有文件需要上传');
        this.results.uploadSuccess = true;
      } else if (this.results.uploadSuccess) {
        console.log('✅ 上传流程执行成功');
      } else {
        console.log(`⚠️ 上传流程部分失败，${failureCount} 个文件上传失败`);
        this.results.errors.push(`上传失败文件数: ${failureCount}`);
      }
      
      return this.results.uploadSuccess;
      
    } catch (error) {
      console.error('❌ 上传流程执行失败:', error.message);
      this.results.uploadSuccess = false;
      this.results.errors.push(`上传流程错误: ${error.message}`);
      return false;
    }
  }

  /**
   * 执行完整工作流
   */
  async executeWorkflow() {
    try {
      console.log('🔗 配置文件同步工作流');
      console.log('📡 自动下载 + 差异上传');
      console.log('='.repeat(60));
      
      // 步骤1: 检查下载缓存
      const cacheValid = this.checkDownloadCache();

      if (!cacheValid) {
        // 检查是否是因为配置版本不匹配导致的缓存无效
        const cacheExists = fs.existsSync(CONFIG.downloadCacheDir);
        const md5Exists = fs.existsSync(CONFIG.md5File);

        if (cacheExists && md5Exists) {
          console.log('\n🔄 配置版本不匹配，清理旧缓存...');
          try {
            fs.rmSync(CONFIG.downloadCacheDir, { recursive: true, force: true });
            console.log('✅ 旧缓存已清理');
          } catch (error) {
            console.log(`⚠️ 清理旧缓存失败: ${error.message}`);
          }
        }

        console.log('\n📥 开始执行下载...');

        // 步骤2: 执行下载
        const downloadSuccess = await this.executeDownload();

        if (!downloadSuccess) {
          console.log('❌ 下载失败，工作流终止');
          return false;
        }
      } else {
        console.log('\n✅ 下载缓存有效且配置版本匹配，跳过下载步骤');
      }
      
      // 步骤3: 执行上传（基于MD5差异）
      console.log('\n📤 开始执行差异上传...');
      const uploadSuccess = await this.executeUpload();
      
      // 生成工作流报告
      this.generateWorkflowReport();
      
      if (uploadSuccess) {
        console.log('\n🎉 工作流执行完成！');
        return true;
      } else {
        console.log('\n⚠️ 工作流部分成功，请检查上传结果');
        return false;
      }
      
    } catch (error) {
      console.error('❌ 工作流执行失败:', error.message);
      this.results.errors.push(`工作流错误: ${error.message}`);
      this.generateWorkflowReport();
      return false;
    }
  }

  /**
   * 生成工作流报告
   */
  generateWorkflowReport() {
    const report = [
      '='.repeat(60),
      '配置文件同步工作流报告',
      '='.repeat(60),
      `执行时间: ${new Date().toLocaleString()}`,
      '',
      '执行步骤:',
      `  下载流程: ${this.results.downloadExecuted ? (this.results.downloadSuccess ? '✅ 成功' : '❌ 失败') : '⏭️ 跳过'}`,
      `  上传流程: ${this.results.uploadExecuted ? (this.results.uploadSuccess ? '✅ 成功' : '❌ 失败') : '❌ 未执行'}`,
      ''
    ];

    if (this.results.downloadExecuted && this.results.downloadResults.length > 0) {
      const successCount = this.results.downloadResults.filter(r => r.status === 'success').length;
      const failureCount = this.results.downloadResults.filter(r => r.status === 'failure').length;
      
      report.push('下载结果:');
      report.push(`  总文件数: ${this.results.downloadResults.length}`);
      report.push(`  成功下载: ${successCount}`);
      report.push(`  下载失败: ${failureCount}`);
      report.push('');
    }

    if (this.results.uploadExecuted && this.results.uploadResults.length > 0) {
      const successCount = this.results.uploadResults.filter(r => r.status === 'success').length;
      const failureCount = this.results.uploadResults.filter(r => r.status === 'failure').length;
      
      report.push('上传结果:');
      report.push(`  总文件数: ${this.results.uploadResults.length}`);
      report.push(`  成功上传: ${successCount}`);
      report.push(`  上传失败: ${failureCount}`);
      report.push('');
    }

    if (this.results.errors.length > 0) {
      report.push('错误信息:');
      this.results.errors.forEach(error => {
        report.push(`  ❌ ${error}`);
      });
      report.push('');
    }

    report.push('='.repeat(60));
    
    const reportContent = report.join('\n');
    
    // 显示报告
    console.log('\n' + reportContent);
    
    // 保存报告
    try {
      fs.writeFileSync(CONFIG.reportFile, reportContent, 'utf8');
      console.log(`📄 工作流报告已保存到: ${CONFIG.reportFile}`);
    } catch (error) {
      console.error('保存工作流报告失败:', error.message);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const args = process.argv.slice(2);

    if (args.includes('--help') || args.includes('-h')) {
      console.log('配置文件同步工作流脚本');
      console.log('');
      console.log('使用方法:');
      console.log('  node workflow.js                    # 执行完整工作流');
      console.log('  node workflow.js --force-download   # 强制重新下载');
      console.log('  node workflow.js --help             # 显示帮助');
      console.log('');
      console.log('工作流程:');
      console.log('  1. 检查下载缓存是否存在');
      console.log('  2. 检查配置版本是否匹配');
      console.log('  3. 如果缓存不存在或版本不匹配，执行下载流程');
      console.log('  4. 基于下载的 MD5 文件，执行差异上传');
      console.log('');
      console.log('环境变量:');
      console.log('  CONFIG_VERSION                      # 配置版本 (必须设置)');
      console.log('  可选值:  pre-version, release');
      return;
    }

    const workflow = new WorkflowManager();
    
    // 检查是否强制重新下载
    if (args.includes('--force-download')) {
      console.log('🔄 强制重新下载模式...');
      if (fs.existsSync(CONFIG.downloadCacheDir)) {
        console.log('🗑️ 删除现有下载缓存...');
        fs.rmSync(CONFIG.downloadCacheDir, { recursive: true, force: true });
      }
    }
    
    // 执行工作流
    const success = await workflow.executeWorkflow();
    
    if (success) {
      process.exit(0);
    } else {
      process.exit(1);
    }
    
  } catch (error) {
    console.error('程序执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { WorkflowManager };
