#!/usr/bin/env node
/**
 * 配置版本切换工具
 * 用于快速切换上传目标环境
 */

import fs from 'fs';
import path from 'path';

const ENV_FILE = '.env';

// 预定义的配置版本
const PREDEFINED_VERSIONS = {
  'pre': 'pre-version',
  'prod': 'production', 
  'staging': 'staging',
  'dev': 'dev',
  'test': 'test'
};

/**
 * 读取当前配置
 */
function getCurrentConfig() {
  try {
    if (!fs.existsSync(ENV_FILE)) {
      return 'pre-version'; // 默认值
    }
    
    const content = fs.readFileSync(ENV_FILE, 'utf8');
    const match = content.match(/CONFIG_VERSION=(.+)/);
    return match ? match[1].trim() : 'pre-version';
  } catch (error) {
    console.error('读取配置失败:', error.message);
    return 'pre-version';
  }
}

/**
 * 设置新配置
 */
function setConfig(version) {
  try {
    let content;
    
    if (fs.existsSync(ENV_FILE)) {
      // 更新现有文件
      content = fs.readFileSync(ENV_FILE, 'utf8');
      content = content.replace(/CONFIG_VERSION=.+/, `CONFIG_VERSION=${version}`);
    } else {
      // 创建新文件
      content = `# 腾讯云 COS 上传配置
# 配置版本标识，用于构建上传路径
CONFIG_VERSION=${version}

# 其他可选配置
# CONFIG_VERSION=pre-version
# CONFIG_VERSION=production
# CONFIG_VERSION=staging
# CONFIG_VERSION=dev
# CONFIG_VERSION=test
`;
    }
    
    fs.writeFileSync(ENV_FILE, content, 'utf8');
    console.log(`✅ 配置已更新: ${version}`);
    console.log(`🎯 上传目标: website_public/_config/${version}/`);
    return true;
  } catch (error) {
    console.error('设置配置失败:', error.message);
    return false;
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log('配置版本切换工具');
  console.log('');
  console.log('使用方法:');
  console.log('  node switch-config.js [version]');
  console.log('');
  console.log('快捷版本:');
  Object.entries(PREDEFINED_VERSIONS).forEach(([short, full]) => {
    console.log(`  ${short.padEnd(8)} -> ${full}`);
  });
  console.log('');
  console.log('示例:');
  console.log('  node switch-config.js pre        # 切换到 pre-version');
  console.log('  node switch-config.js prod       # 切换到 production');
  console.log('  node switch-config.js v1.0.0     # 切换到自定义版本');
  console.log('  node switch-config.js            # 显示当前配置');
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  const currentConfig = getCurrentConfig();
  
  if (args.length === 0) {
    // 显示当前配置
    console.log(`📁 当前配置版本: ${currentConfig}`);
    console.log(`🎯 上传目标: website_public/_config/${currentConfig}/`);
    console.log('');
    console.log('使用 --help 查看切换选项');
    return;
  }
  
  const inputVersion = args[0];
  
  // 检查是否是快捷版本
  const targetVersion = PREDEFINED_VERSIONS[inputVersion] || inputVersion;
  
  if (targetVersion === currentConfig) {
    console.log(`ℹ️  配置版本已经是: ${targetVersion}`);
    return;
  }
  
  console.log(`🔄 切换配置版本: ${currentConfig} -> ${targetVersion}`);
  
  if (setConfig(targetVersion)) {
    console.log('');
    console.log('💡 提示: 切换配置后建议重新初始化 MD5 记录');
    console.log('   node upload-json-files.js --init');
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
